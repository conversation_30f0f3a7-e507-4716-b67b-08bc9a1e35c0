package com.faw.work.ais.aic.feign;

import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.model.request.AfterEmotionCallbackRequest;
import com.faw.work.ais.aic.model.response.DmsEmotionResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "DGWFeignClient", url = "${aic.config.ucg.host}", configuration = AicAuthFeignInterceptor.class)
public interface DgwOpenApiFeignClient {

    /**
     * 情绪价值模型回调
     *
     * @param request 要求
     * @return {@link DgwResult }
     */
    @PostMapping("/JT/SA/SA-0214/DGW/serviceInspection/aiSummaryCallback")
    DgwResult callBackDmsEmotion(@RequestBody DmsEmotionResponse request);

    /**
     * 售后情绪价值模型回调
     *
     * @param request 请求
     * @return {@link DgwResult }
     */
    @PostMapping("/JT/SA/SA-0202/001/DEFAULT/wechat/emotion/emotionAiCallback")
    DgwResult callBackAfterEmotion(@RequestBody AfterEmotionCallbackRequest request);
}