# 售后接待情绪价值模型API文档（更新版）

## 概述

售后接待情绪价值模型是一个用于分析售后服务对话中客户情绪和服务质量的AI系统。该系统接收对话内容，进行分片处理，调用大模型进行情绪分析和对话总结，最后将结果回调给上游系统。

## 数据结构更新说明

**重要变更**: 根据最新的模型返回格式调整，`questions`字段已从对话总结模型移至情绪分析模型中。系统会自动聚合所有分片中的questions并在回调时一并返回。

## API接口

### 售后接待情绪价值模型数据接收接口

**接口地址**: `POST /llm-record/process-after-emotion`

**请求参数**:
```json
{
  "requestId": "unique-request-id",
  "userInput": "(start:0000) 接待专员：您好，我是一汽红旗的售后服务顾问... (end:5000)\n(start:6000) 客户：我的车子发动机有异响... (end:7000)",
  "audioUrl": "https://example.com/audio.mp3"
}
```

**响应示例**:
```json
{
  "code": "200",
  "message": "success",
  "data": "成功接收消息，请关注回调"
}
```

## 大模型配置

### 情绪分析模型（更新）
- **应用ID**: `b793a2eae5064584998e6abd3706e399`
- **输出格式**:
```json
{
  "emotion": "十分满意",
  "reason": "服务顾问礼貌询问客户的来意，客户对服务顾问的态度表现出满意的态度",
  "labels": ["服务态度", "忠诚表达"],
  "start": "12:00:00",
  "end": "12:00:40",
  "score": 90,
  "questions": [
    {
      "question": "用户询问店里是否有优惠活动",
      "answer": "顾问表示有很多，并邀请客户仔细查看",
      "emotion": "一般满意"
    }
  ]
}
```

### 对话总结模型（更新）
- **应用ID**: `1407e68dc5974c7987beb81d199b0534`
- **输出格式**:
```json
{
  "totalEmotion": "客户总体情绪评级",
  "summary": "本次接待过程的综合总结，包括产品介绍情况、客户反馈、购买意向等关键信息",
  "overallPerformance": "接待人员整体表现评价",
  "customerConcerns": "客户重点关注和感兴趣的产品特性、功能点，以及给予好评的方面",
  "followUpSuggestions": "基于客户反馈和疑虑，提供针对性的下次沟通建议和策略"
}
```

## 回调数据结构

系统处理完成后，会将以下数据结构回调给上游：

```json
{
  "requestId": "unique-request-id",
  "emotionAnalysisList": [
    {
      "emotion": "十分满意",
      "reason": "服务顾问礼貌询问客户的来意",
      "labels": ["服务态度", "忠诚表达"],
      "start": "12:00:00",
      "end": "12:00:40",
      "score": 90,
      "questions": [
        {
          "question": "用户询问店里是否有优惠活动",
          "answer": "顾问表示有很多，并邀请客户仔细查看",
          "emotion": "一般满意"
        }
      ]
    }
  ],
  "conversationSummary": {
    "totalEmotion": "客户总体情绪评级",
    "summary": "本次接待过程的综合总结",
    "overallPerformance": "接待人员整体表现评价",
    "customerConcerns": "客户重点关注的方面",
    "followUpSuggestions": "下次沟通建议"
  },
  "allQuestions": [
    {
      "question": "用户询问店里是否有优惠活动",
      "answer": "顾问表示有很多，并邀请客户仔细查看",
      "emotion": "一般满意"
    },
    {
      "question": "客户询问发动机异响是否在保修范围内",
      "answer": "顾问表示需要检查后确定",
      "emotion": "担心"
    }
  ],
  "processTime": "2024-01-15 14:30:25"
}
```

## 处理流程

1. **接收请求** → 2. **数据分片** → 3. **存储分片** → 4. **发送MQ消息** → 5. **消费消息** → 6. **情绪分析** → 7. **对话总结** → 8. **聚合Questions** → 9. **回调上游**

## 核心变更

### 数据聚合逻辑
- 情绪分析模型现在返回`questions`字段
- 系统会自动聚合所有分片中的`questions`
- 回调时提供`allQuestions`字段包含所有问答对
- 对话总结模型不再返回`questions`字段

### 代码调整
- 更新了`EmotionAnalysisResult`类，添加了`questions`字段
- 更新了`ConversationSummaryResult`类，移除了`questions`字段
- 更新了`AfterEmotionCallbackRequest`类，添加了`allQuestions`字段
- 调整了`processAfterEmotionSlice`方法中的数据聚合逻辑

## 测试验证

可以使用以下测试数据验证新的逻辑：

```java
// 情绪分析结果示例（包含questions）
{
  "emotion": "一般满意",
  "reason": "服务顾问礼貌询问客户的来意",
  "labels": ["服务态度", "忠诚表达"],
  "start": "12:00:00",
  "end": "12:00:40",
  "score": 90,
  "questions": [
    {
      "question": "用户询问店里是否有优惠活动",
      "answer": "顾问表示有很多，并邀请客户仔细查看",
      "emotion": "一般满意"
    }
  ]
}
```

## 注意事项

1. 确保大模型返回的JSON格式符合新的数据结构
2. 系统会自动聚合所有分片中的questions
3. 回调数据中包含完整的情绪分析列表和聚合的问答列表
4. 建议在生产环境部署前进行充分测试
