package com.faw.work.ais.aic.model.request;

import com.faw.work.ais.aic.model.response.ConversationSummaryResult;
import com.faw.work.ais.aic.model.response.EmotionAnalysisResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 售后接待情绪价值模型回调请求
 * <AUTHOR>
 */
@Data
@Schema(description = "售后接待情绪价值模型回调请求")
public class AfterEmotionCallbackRequest {

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "情绪分析结果列表")
    private List<EmotionAnalysisResult> emotionAnalysisList;

    @Schema(description = "对话总结结果")
    private ConversationSummaryResult conversationSummary;

    @Schema(description = "所有问答列表（从各分片聚合）")
    private List<EmotionAnalysisResult.QuestionAnswer> allQuestions;

    @Schema(description = "处理时间")
    private String processTime;

    @Schema(description = "售后阶段 P01-服务接待 P02-需求确认 P03-交车检查 P04-结算送行 ")
    private String phaseCode;
}
