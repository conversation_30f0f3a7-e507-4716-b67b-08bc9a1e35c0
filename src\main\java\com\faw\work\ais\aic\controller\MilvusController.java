package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.Response;
import com.google.gson.JsonObject;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.*;
import io.milvus.param.ConnectParam;
import io.milvus.param.R;
import io.milvus.param.collection.DescribeCollectionParam;
import io.milvus.param.collection.GetCollectionStatisticsParam;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.QueryParam;
import io.milvus.response.QueryResultsWrapper;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Milvus向量数据库操作控制器
 *
 * <AUTHOR>
 */
@Tag(name = "Milvus向量数据库操作控制器", description = "Milvus向量数据库操作控制器")
@Api(tags = "向量数据库管理")
@RestController
@RequestMapping("/milvus")
@RequiredArgsConstructor
@Slf4j
public class MilvusController {

    private final MilvusPoolConfig milvusPoolConfig;

    private final MilvusService milvusService;


    private static final String SOURCE_HOST = "c-649f5b46b97336c5.milvus.aliyuncs.com";
    private static final int SOURCE_PORT = 19530;
    private static final String SOURCE_USERNAME = "FawVoice";
    private static final String SOURCE_PASSWORD = "Admin199401@";
    private static final String SOURCE_DB_NAME = "aio";

    private static final String TARGET_HOST = "c-ceddcf5be4375f2c.milvus.aliyuncs.com";
    private static final int TARGET_PORT = 19530;
    private static final String TARGET_USERNAME = "FawVoice";
    private static final String TARGET_PASSWORD = "Admin199401@";
    private static final String TARGET_DB_NAME = "aio";

    private static final int BATCH_SIZE = 500;
    public static final String DIM = "dim";

    /**
     * 创建Milvus连接并检测集合是否存在
     *
     * @return 集合是否存在
     */
    @Operation(summary = "检测集合是否存在", description = "[author:10200571]")
    @PostMapping("/check-collection")
    public Response<Boolean> checkCollection() {


        boolean exists = false;

        try {

            // ConnectConfig connectConfig = ConnectConfig.builder()
            //         .uri(String.format("%s:%d", milvusPoolConfig.getHost(), milvusPoolConfig.getPort()))
            //         .username(milvusPoolConfig.getUsername())
            //         .password(milvusPoolConfig.getPassword())
            //         .dbName(milvusPoolConfig.getDbName())
            //         .build();
            // MilvusClientV2  client = new MilvusClientV2(connectConfig);

            MilvusClientV2 client = milvusPoolConfig.createTemporaryClient("check_collection");
            // 检测集合是否存在
            Boolean lingxiaoxi = client.hasCollection(HasCollectionReq.builder().collectionName("lingxiaoxi").build());
            log.info("是否存在lingxiaoxi: {}", lingxiaoxi);
            // 关闭连接
            client.close();

        } catch (Exception e) {
            log.error("检测集合是否存在时发生错误: ", e);
            return Response.fail("检测集合时发生错误: " + e.getMessage());
        }

        return Response.success(exists);
    }



    /**
     * 迁移Milvus集合数据
     * @param sourceCollection 源集合名称
     * @param targetCollection 目标集合名称
     * @return 迁移结果
     */
    @PostMapping("/migrate-collection")
    @Operation(summary = "迁移Milvus集合数据", description = "[author:10200571]")
    public AiResult<Map<String, Long>> migrateCollection(
            @RequestParam String sourceCollection,
            @RequestParam String targetCollection) {
        Map<String, Long> result = new HashMap<>(32);
        MilvusServiceClient sourceClient = null;
        MilvusServiceClient targetClient = null;

        try {
            // 创建源环境和目标环境的连接
            sourceClient = createClient(SOURCE_HOST, SOURCE_PORT, SOURCE_USERNAME, SOURCE_PASSWORD, SOURCE_DB_NAME);
            targetClient = createClient(TARGET_HOST, TARGET_PORT, TARGET_USERNAME, TARGET_PASSWORD, TARGET_DB_NAME);
            log.info("成功连接到源和目标Milvus实例。");

            // 验证集合是否存在
            if (!checkCollectionExists(sourceClient, sourceCollection)) {
                return AiResult.fail("源集合不存在: " + sourceCollection);
            }
            if (!checkCollectionExists(targetClient, targetCollection)) {
                return AiResult.fail("目标集合不存在: " + targetCollection);
            }
            log.info("源和目标集合均存在。");

            // 验证集合schema是否一致
            if (!validateCollectionSchema(sourceClient, targetClient, sourceCollection, targetCollection)) {
                return AiResult.fail("源集合和目标集合的schema不一致");
            }
            log.info("源和目标集合的schema一致。");

            // 获取总记录数
            R<GetCollectionStatisticsResponse> statR = sourceClient.getCollectionStatistics(GetCollectionStatisticsParam.newBuilder().withCollectionName(sourceCollection).build());
            if (statR.getStatus() != R.Status.Success.getCode()) {
                throw new RuntimeException("获取源集合统计信息失败: " + statR.getMessage());
            }
            long totalCount = Long.parseLong(statR.getData().getStats(0).getValue());
            result.put("totalCount", totalCount);
            log.info("源集合 '{}' 总记录数: {}", sourceCollection, totalCount);

            // 分批迁移数据
            long migratedCount = migrateData(sourceClient, targetClient, sourceCollection, targetCollection, totalCount);
            result.put("migratedCount", migratedCount);

            return AiResult.success(result);
        } catch (Exception e) {
            log.error("迁移集合数据失败", e);
            return AiResult.fail("迁移失败: " + e.getMessage());
        } finally {
            if (sourceClient != null) {
                sourceClient.close();
            }
            if (targetClient != null) {
                targetClient.close();
            }
        }
    }

    private MilvusServiceClient createClient(String host, int port, String user, String password, String dbName) {
        ConnectParam connectParam = ConnectParam.newBuilder()
                .withHost(host)
                .withPort(port)
                .withAuthorization(user, password)
                .withDatabaseName(dbName)
                .build();
        return new MilvusServiceClient(connectParam);
    }

    private boolean checkCollectionExists(MilvusServiceClient client, String collectionName) {
        R<Boolean> response = client.hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("检查集合是否存在时发生错误: " + response.getMessage());
        }
        return response.getData();
    }


    private boolean validateCollectionSchema(MilvusServiceClient sourceClient, MilvusServiceClient targetClient,
                                             String sourceCollection, String targetCollection) {

        R<DescribeCollectionResponse> sourceR = sourceClient.describeCollection(DescribeCollectionParam.newBuilder().withCollectionName(sourceCollection).build());
        R<DescribeCollectionResponse> targetR = targetClient.describeCollection(DescribeCollectionParam.newBuilder().withCollectionName(targetCollection).build());

        if (sourceR.getStatus() != R.Status.Success.getCode() || targetR.getStatus() != R.Status.Success.getCode()){
            throw new RuntimeException("获取集合Schema信息失败");
        }
        CollectionSchema sourceSchema = sourceR.getData().getSchema();
        CollectionSchema targetSchema = targetR.getData().getSchema();

        Map<String, FieldSchema> sourceFields = sourceSchema.getFieldsList().stream()
                .collect(Collectors.toMap(FieldSchema::getName, f -> f));

        Map<String, FieldSchema> targetFields = targetSchema.getFieldsList().stream()
                .collect(Collectors.toMap(FieldSchema::getName, f -> f));

        if (sourceFields.size() != targetFields.size()) {
            log.error("字段数量不匹配: 源 {}, 目标 {}", sourceFields.size(), targetFields.size());
            return false;
        }

        for (Map.Entry<String, FieldSchema> entry : sourceFields.entrySet()) {
            String fieldName = entry.getKey();
            FieldSchema sourceField = entry.getValue();
            FieldSchema targetField = targetFields.get(fieldName);

            if (targetField == null) {
                log.error("目标集合缺少字段: {}", fieldName);
                return false;
            }

            if (sourceField.getDataType() != targetField.getDataType()) {
                log.error("字段 '{}' 类型不匹配: 源 {}, 目标 {}", fieldName, sourceField.getDataType(), targetField.getDataType());
                return false;
            }

            if (sourceField.getIsPrimaryKey() != targetField.getIsPrimaryKey()) {
                log.error("字段 '{}' 主键属性不匹配", fieldName);
                return false;
            }

            if (sourceField.getDataType() == DataType.FloatVector) {
                int sourceDim = Integer.parseInt(sourceField.getTypeParamsList().stream().filter(p->p.getKey().equals(DIM)).findFirst().get().getValue());
                int targetDim = Integer.parseInt(targetField.getTypeParamsList().stream().filter(p->p.getKey().equals(DIM)).findFirst().get().getValue());
                if (sourceDim != targetDim) {
                    log.error("向量字段 '{}' 维度不匹配: 源 {}, 目标 {}", fieldName, sourceDim, targetDim);
                    return false;
                }
            }
        }

        return true;
    }

    private long migrateData(MilvusServiceClient sourceClient, MilvusServiceClient targetClient,
                             String sourceCollection, String targetCollection, long totalCount) {
        long offset = 0;
        long migratedCount = 0;

        R<DescribeCollectionResponse> sourceR = sourceClient.describeCollection(DescribeCollectionParam.newBuilder().withCollectionName(sourceCollection).build());
        CollectionSchema sourceSchema = sourceR.getData().getSchema();

        List<String> outputFields = sourceSchema.getFieldsList().stream()
                .map(FieldSchema::getName)
                .collect(Collectors.toList());

        FieldSchema pkField = sourceSchema.getFieldsList().stream()
                .filter(FieldSchema::getIsPrimaryKey)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("源集合没有找到主键字段"));

        String pkFieldName = pkField.getName();
        String queryFilter = pkField.getDataType() == DataType.VarChar ? pkFieldName + " != \"\"" : pkFieldName + " > 0";


        while (offset < totalCount) {
            QueryParam queryParam = QueryParam.newBuilder()
                    .withCollectionName(sourceCollection)
                    .withExpr(queryFilter)
                    .withLimit((long) BATCH_SIZE)
                    .withOffset(offset)
                    .withOutFields(outputFields)
                    .build();

            R<QueryResults> queryR = sourceClient.query(queryParam);
            if (queryR.getStatus() != R.Status.Success.getCode()) {
                throw new RuntimeException("查询数据失败: " + queryR.getMessage());
            }

            QueryResultsWrapper wrapper = new QueryResultsWrapper(queryR.getData());
            if (wrapper.getFieldWrapper(pkFieldName).getRowCount() == 0) {
                break;
            }

            List<JsonObject> rows = new ArrayList<>();
            int rowCount = (int) wrapper.getFieldWrapper(pkFieldName).getRowCount();
            for (int i = 0; i < rowCount; ++i) {
                JsonObject row = new JsonObject();
                for (String fieldName : outputFields) {
                    Object val = wrapper.getFieldWrapper(fieldName).getFieldData().get(i);
                    // GSON的JsonObject需要根据不同类型添加
                    if (val instanceof String) {
                        row.addProperty(fieldName, (String) val);
                    } else if (val instanceof Number) {
                        row.addProperty(fieldName, (Number) val);
                    } else if (val instanceof Boolean) {
                        row.addProperty(fieldName, (Boolean) val);
                    } else if (val instanceof List) {
                        row.add(fieldName, new com.google.gson.Gson().toJsonTree(val));
                    } else {
                        // 对于其他类型，转为String处理
                        row.addProperty(fieldName, val.toString());
                    }
                }
                rows.add(row);
            }

            if (rows.isEmpty()) {
                break;
            }

            InsertParam insertRequest = InsertParam.newBuilder()
                    .withCollectionName(targetCollection)
                    .withRows(rows)
                    .build();
            targetClient.insert(insertRequest);

            migratedCount += rows.size();
            offset += BATCH_SIZE;

            log.info("已迁移 {}/{} 条记录", migratedCount, totalCount);
        }

        return migratedCount;
    }
} 