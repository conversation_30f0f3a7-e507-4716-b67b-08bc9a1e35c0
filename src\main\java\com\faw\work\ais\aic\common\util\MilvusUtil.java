package com.faw.work.ais.aic.common.util;

import cn.hutool.core.util.StrUtil;
import io.milvus.client.MilvusServiceClient;
import io.milvus.grpc.*;
import io.milvus.param.ConnectParam;
import io.milvus.param.R;
import io.milvus.param.collection.DescribeCollectionParam;
import io.milvus.param.collection.HasCollectionParam;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Milvus向量数据库工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class MilvusUtil {

    public static final String DIM = "dim";

    /**
     * 创建Milvus客户端连接
     * 
     * @param host 主机地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param dbName 数据库名称
     * @return Milvus客户端
     */
    public static MilvusServiceClient createClient(String host, int port, String username, String password, String dbName) {
        if (StrUtil.isBlank(host) || StrUtil.isBlank(username) || StrUtil.isBlank(password) || StrUtil.isBlank(dbName)) {
            throw new IllegalArgumentException("Milvus连接参数不能为空");
        }
        
        ConnectParam connectParam = ConnectParam.newBuilder()
                .withHost(host)
                .withPort(port)
                .withAuthorization(username, password)
                .withDatabaseName(dbName)
                .build();
        return new MilvusServiceClient(connectParam);
    }

    /**
     * 检查集合是否存在
     * 
     * @param client Milvus客户端
     * @param collectionName 集合名称
     * @return 集合是否存在
     */
    public static boolean checkCollectionExists(MilvusServiceClient client, String collectionName) {
        if (client == null || StrUtil.isBlank(collectionName)) {
            throw new IllegalArgumentException("客户端和集合名称不能为空");
        }
        
        R<Boolean> response = client.hasCollection(HasCollectionParam.newBuilder().withCollectionName(collectionName).build());
        if (response.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("检查集合是否存在时发生错误: " + response.getMessage());
        }
        return response.getData();
    }

    /**
     * 验证两个集合的Schema是否一致
     * 
     * @param sourceClient 源客户端
     * @param targetClient 目标客户端
     * @param sourceCollection 源集合名称
     * @param targetCollection 目标集合名称
     * @return Schema是否一致
     */
    public static boolean validateCollectionSchema(MilvusServiceClient sourceClient, MilvusServiceClient targetClient,
                                                   String sourceCollection, String targetCollection) {
        if (sourceClient == null || targetClient == null || 
            StrUtil.isBlank(sourceCollection) || StrUtil.isBlank(targetCollection)) {
            throw new IllegalArgumentException("客户端和集合名称不能为空");
        }

        R<DescribeCollectionResponse> sourceR = sourceClient.describeCollection(
                DescribeCollectionParam.newBuilder().withCollectionName(sourceCollection).build());
        R<DescribeCollectionResponse> targetR = targetClient.describeCollection(
                DescribeCollectionParam.newBuilder().withCollectionName(targetCollection).build());

        if (sourceR.getStatus() != R.Status.Success.getCode() || targetR.getStatus() != R.Status.Success.getCode()) {
            throw new RuntimeException("获取集合Schema信息失败");
        }
        
        CollectionSchema sourceSchema = sourceR.getData().getSchema();
        CollectionSchema targetSchema = targetR.getData().getSchema();

        Map<String, FieldSchema> sourceFields = sourceSchema.getFieldsList().stream()
                .collect(Collectors.toMap(FieldSchema::getName, f -> f));

        Map<String, FieldSchema> targetFields = targetSchema.getFieldsList().stream()
                .collect(Collectors.toMap(FieldSchema::getName, f -> f));

        if (sourceFields.size() != targetFields.size()) {
            log.error("字段数量不匹配: 源 {}, 目标 {}", sourceFields.size(), targetFields.size());
            return false;
        }

        for (Map.Entry<String, FieldSchema> entry : sourceFields.entrySet()) {
            String fieldName = entry.getKey();
            FieldSchema sourceField = entry.getValue();
            FieldSchema targetField = targetFields.get(fieldName);

            if (targetField == null) {
                log.error("目标集合缺少字段: {}", fieldName);
                return false;
            }

            if (sourceField.getDataType() != targetField.getDataType()) {
                log.error("字段 '{}' 类型不匹配: 源 {}, 目标 {}", fieldName, sourceField.getDataType(), targetField.getDataType());
                return false;
            }

            if (sourceField.getIsPrimaryKey() != targetField.getIsPrimaryKey()) {
                log.error("字段 '{}' 主键属性不匹配", fieldName);
                return false;
            }

            if (sourceField.getDataType() == DataType.FloatVector) {
                int sourceDim = Integer.parseInt(sourceField.getTypeParamsList().stream()
                        .filter(p -> p.getKey().equals(DIM)).findFirst().get().getValue());
                int targetDim = Integer.parseInt(targetField.getTypeParamsList().stream()
                        .filter(p -> p.getKey().equals(DIM)).findFirst().get().getValue());
                if (sourceDim != targetDim) {
                    log.error("向量字段 '{}' 维度不匹配: 源 {}, 目标 {}", fieldName, sourceDim, targetDim);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 安全关闭Milvus客户端
     * 
     * @param client Milvus客户端
     */
    public static void closeClient(MilvusServiceClient client) {
        if (client != null) {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("关闭Milvus客户端时发生异常", e);
            }
        }
    }
}
