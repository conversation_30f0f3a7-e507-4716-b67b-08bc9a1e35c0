package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 情绪分析结果
 * <AUTHOR>
 */
@Data
@Schema(description = "情绪分析结果")
public class EmotionAnalysisResult {

    @Schema(description = "情绪状态")
    private String emotion;

    @Schema(description = "情绪原因")
    private String reason;

    @Schema(description = "标签列表")
    private List<String> labels;

    @Schema(description = "开始时间")
    private String start;

    @Schema(description = "结束时间")
    private String end;

    @Schema(description = "情绪评分")
    private Integer score;

    @Schema(description = "问答列表")
    private List<QuestionAnswer> questions;

    /**
     * 问答对象
     */
    @Data
    @Schema(description = "问答对象")
    public static class QuestionAnswer {
        @Schema(description = "问题")
        private String question;

        @Schema(description = "答案")
        private String answer;

        @Schema(description = "情绪")
        private String emotion;
    }
}
