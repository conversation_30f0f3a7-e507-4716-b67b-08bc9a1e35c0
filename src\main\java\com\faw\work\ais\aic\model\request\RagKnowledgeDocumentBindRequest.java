package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 知识库文档绑定请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档绑定请求")
public class RagKnowledgeDocumentBindRequest {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long ragKnowledgeId;

    @Schema(description = "文档ID列表", required = true)
    private List<Long> documentIds;

    @Schema(description = "文档类目", required = true)
    private Long categoryId;
}
