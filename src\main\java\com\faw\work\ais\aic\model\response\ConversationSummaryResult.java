package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 对话总结结果
 * <AUTHOR>
 */
@Data
@Schema(description = "对话总结结果")
public class ConversationSummaryResult {

    @Schema(description = "客户总体情绪评级")
    private String totalEmotion;

    @Schema(description = "本次接待过程的综合总结，包括产品介绍情况、客户反馈、购买意向等关键信息")
    private String summary;

    @Schema(description = "接待人员整体表现评价")
    private String overallPerformance;

    @Schema(description = "客户重点关注和感兴趣的产品特性、功能点，以及给予好评的方面")
    private String customerConcerns;

    @Schema(description = "基于客户反馈和疑虑，提供针对性的下次沟通建议和策略")
    private String followUpSuggestions;
}
