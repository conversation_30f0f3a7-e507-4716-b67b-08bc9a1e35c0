package com.faw.work.ais.aic.common.mq;

import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 售后接待情绪价值模型消息生产者
 * <AUTHOR>
 */
@Slf4j
@Service
public class AfterEmotionMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MessageQueueMapper messageQueueMapper;

    @Value("${rabbitmq.after-emotion.exchange.name:after-emotion-processing-exchange}")
    private String exchangeName;

    @Value("${rabbitmq.after-emotion.routing.key:after-emotion-processing}")
    private String routingKey;

    @Value("${rabbitmq.after-emotion.queue.name:after-emotion-processing-queue}")
    private String queueName;

    /**
     * 发送售后接待情绪分析消息
     *
     * @param requestId 请求ID
     * @param bizType   业务类型
     */
    public void sendAfterEmotionMessage(String requestId, String bizType) {

        try {
            // 生成消息ID
            String messageId = UUID.randomUUID().toString();

            // 先保存到本地消息表
            MessageQueue messageQueue = new MessageQueue();
            messageQueue.setMessageId(messageId);
            messageQueue.setMessageContent(requestId);
            messageQueue.setQueueName(queueName);
            messageQueue.setBizType(bizType);
            messageQueue.setStatus(0);
            messageQueue.setCreateTime(LocalDateTime.now());
            messageQueue.setUpdateTime(LocalDateTime.now());
            messageQueue.setRetryCount(0);

            int result = messageQueueMapper.insert(messageQueue);
            if (result <= 0) {
                log.error("保存消息到本地消息表失败，messageId: {}", messageId);
                throw new RuntimeException("保存消息到本地消息表失败");
            }

            log.info("成功保存消息到本地消息表，messageId: {}, requestId: {}, bizType: {}", messageId, requestId, bizType);

            // 发送消息到RabbitMQ
            rabbitTemplate.convertAndSend(exchangeName, routingKey, requestId, message -> {
                message.getMessageProperties().setMessageId(messageId);
                return message;
            });

            log.info("成功发送售后接待情绪分析消息，messageId: {}, requestId: {}, bizType: {}", messageId, requestId, bizType);

        } catch (Exception e) {
            log.error("发送售后接待情绪分析消息失败，requestId: {}, bizType: {}", requestId, bizType, e);
            throw new RuntimeException("发送消息失败: " + e.getMessage(), e);
        }
    }
}
